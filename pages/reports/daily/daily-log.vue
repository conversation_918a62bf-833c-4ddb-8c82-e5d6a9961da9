<template>
  <div class="px-5 py-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center py-5">
      <img src="@/assets/icons/report.png" alt="report-icon" class="w-8 h-8 mr-2" />
      <h3 class="text-2xl font-semibold uppercase">Daily Log Report</h3>
    </div>

    <div class="flex items-center">
      <div class="bg-white flex space-x-5 py-5 flex-wrap">
        <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
          <FunnelIcon class="w-5 h-5 mr-2" />
          Filter By Date Range
          <div class="w-72 ml-2">
            <datepicker required position="left" placeholder="select start & end date" :range="true" format="dd/MM/yyyy"
              @cleared="clearDateRange" input-class-name="datepicker" v-model="dateRange" :max-date="new Date()"/>
          </div>
        </div>
        <div class="flex flex-col">
          <CoreDropdown :items="departments" v-model="selectedDepartment" />
        </div>
        <div class="flex flex-col">
          <CoreDropdown :items="testTypes" v-model="selectedtTestType" />
        </div>
        <div class="flex flex-col">
          <CoreDropdown :items="testStatuses" v-model="selectedTestStatus" />
        </div>
        <div class="flex flex-col">
          <CoreDropdown :items="reportTypes" v-model="reportType" />
        </div>

        <CoreActionButton color="primary" text="Generate Report" :icon="generateIcon" :loading="loading"
          :click="() => generateReport()"></CoreActionButton>

        <excel class="btn btn-default" :header="[
          `DAILY LOG ${reportType.name.toUpperCase()} REPORT`,
          `PERIOD: ${startDate} - ${endDate}`,
          facility.details.name,
          facility.details.address,
          facility.details.phone,
        ]" :data="exportData" worksheet="report-work-sheet" :name="`daily_log_${reportType.value}_report.xls`">
          <CoreExportButton text="Export Excel" />
        </excel>
      </div>
    </div>

    <div class="border rounded">
      <div class="flex items-center justify-between px-5 py-5 border-b">
        <div class="flex flex-col space-y-2">
          <img src="@/assets/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
          <h3 class="text-xl font-semibold">DAILY LOG REPORT</h3>
        </div>
        <ReportsAddress />
      </div>

      <div class="px-5 py-3">
        <h3 v-if="
          reportType.value == 'test_record' &&
          selectedTestStatus.name !== 'test-rejected'
        " class="text-lg font-semibold">
          Test Record:
          <span class="text-md font-normal">{{ completeTests.toLocaleString() }} completed tests</span>
        </h3>
        <h3 class="text-lg font-semibold">
          Tests Performed Period:
          <span class="text-md font-normal">{{ startDate ? moment(startDate).format(DATE_FORMAT) : "" }} - {{ endDate ?
            moment(endDate).format(DATE_FORMAT) : "" }}</span>
        </h3>
        <h3 v-if="selectedTestStatus.name === 'test-rejected'" class="text-lg font-semibold">
          {{ capitalizeStr(selectedTestStatus.name.replaceAll('-', ' ')) }}:
          <span class="font-normal">{{ reportData?.data ? reportData?.data?.length : 0 }}</span>
        </h3>
      </div>

      <div class="w-full border-t" v-if="reportData?.data?.length > 0">
        <DailyLogTestRecords :data="reportData" v-if="
          reportType.value == 'test_record' &&
          selectedTestStatus.name !== 'test-rejected'
        " />
        <DailyLogPatientRecords :data="reportData" v-if="reportType.value == 'patient_record'" />
        <DailyLogSpecimenRejected :data="reportData" v-if="selectedTestStatus.name == 'test-rejected'" />
      </div>

      <div v-if="reportData.length == 0 && !loading"
        class="w-full flex flex-col items-center justify-center space-y-2 py-10">
        <img src="@/assets/images/page.png" alt="page-icon" class="object-cover w-20 h-20" />
        <p>Data not found, please generate report</p>
      </div>

      <div v-if="loading && reportData.length == 0"
        class="mx-auto justify-center flex flex-col items-center space-y-3 py-10">
        <CoreLoader />
        <p class="text-base">
          Generating report, please wait<span class="animate-ping">...</span>
        </p>
        <button class="font-medium text-base text-sky-500" @click="cancelReportGeneration">Cancel</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  FunnelIcon,
  ArrowPathIcon as generateIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import fetchRequest, { RequestCancellation } from "@/services/fetch";
import { useFacilityStore } from "@/store/facility";
import type {
  Department,
  DropdownItem,
  Page,
  Request,
  Response,
  TestType,
} from "@/types";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ['reports']
});
useHead({
  title: `${Package.name} - Daily Log Reports`,
});

const facility = useFacilityStore();
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Reports",
    link: "#",
  },
]);
const cookie = useCookie("token");
const departments = ref<Array<Department>>([]);
const selectedDepartment = ref<Department>({ name: "select department" });
const testTypes = ref<Array<TestType>>([]);
const cancellation = new RequestCancellation();
const selectedtTestType = ref<DropdownItem>({ name: "select test type"});
const testStatuses = ref<Array<DropdownItem>>([]);
const selectedTestStatus = ref<DropdownItem>({ name: "select test status" });
const dateRange = ref<Array<string>>(["", ""]);
const reportData = ref<any>({});
const completeTests = ref<number>(0);
const reportTypes = ref<Array<{ name: string; value: string }>>([
  {
    name: "Test record",
    value: "test_record",
  },
  {
    name: "Patient record",
    value: "patient_record",
  },
]);
const reportType = ref<Record<string, string>>({
  name: "select report type",
  value: "select report type",
});
const loading = ref<boolean>(false);
const startDate = computed(() => {
  return dateRange.value[0]
    ? moment(dateRange.value[0]).format("YYYY-MM-DD")
    : "";
});
const endDate = computed(() => {
  return dateRange.value[1]
    ? moment(dateRange.value[1]).format("YYYY-MM-DD")
    : "";
});

const clearDateRange = () => {
  dateRange.value = ['', ''];
}

async function getDepartments(): Promise<void> {
  const request: Request = {
    route: endpoints.departments,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    departments.value = data.value;
  }
  if (error.value) {
    console.error(error.value);
  }
}
async function getTestTypes(): Promise<void> {
  const request: Request = {
    route: `${endpoints.testTypes}/by_department?department_id=${selectedDepartment.value.id}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    testTypes.value = data.value;
  }
  if (error.value) {
    console.error(error.value);
  }
}
function checkStatus(status: string): string {
  let returnValue = "";
  if (
    status == "select department" ||
    status === "select test type" ||
    status === "select test status"
  ) {
    returnValue = "";
  } else {
    returnValue = status;
  }

  return returnValue;
}
async function getTestStatuses(): Promise<void> {
  const request: Request = {
    route: `${endpoints.testStatus}/all`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error }: Response = await fetchRequest(request);
  if (data.value) {
    testStatuses.value = data.value;
  }
  if (error.value) {
    console.error(error.value);
  }
}
const extractResults = (
  results: { [s: string]: unknown } | ArrayLike<unknown>
): string => {
  return Object.entries(results)
    .map(([key, value]) => `${key}: ${value}`)
    .join(", ");
};
const exportData = computed(() => {
  let t = new Array<any>();
  reportType.value.value == "test_record" &&
    reportData.value.data &&
    reportData.value.data.map((data: any) => {
      t.push({
        "PATIENT ID": data.patient_id,
        "PATIENT NAME": data.patient_name,
        "ACCESSION NUMBER": data.accession_number,
        SPECIMEN: data.specimen,
        "RECEIPT DATE": data.created_date ? moment(data.created_date).format(DATE_TIME_FORMAT) : "",
        TEST: data.test,
        "PERFORMED BY": data.performed_by,
        RESULTS: extractResults(data.results),
        REMARKS: data.remarks,
        "RESULTS ENTRY DATE": data.result_date ? moment(data.result_date).format(DATE_TIME_FORMAT) : "",
        "AUTHORIZED BY": data.authorized_by,
        "AUTHORIZED DATE": data.authorized_date ? moment(data.authorized_date).format(DATE_TIME_FORMAT) : "",
        "TURN AROUND TIME": data.tat,
      });
    });

  reportType.value.value == "patient_record" &&
    reportData.value.data &&
    reportData.value.data.map((data: any) => {
      t.push({
        "PATIENT ID": data.patient_no,
        "PATIENT NAME": data.patient_name,
        AGE: calculateAge(data.dob),
        SEX: data.gender,
        "ACCESSION NUMBER": data.accession_number,
        "SPECIMEN TYPE": data.specimen,
        TESTS: data.test_type.join(", "),
      });
    });
  return t;
});

const cancelReportGeneration = (): void => {
  cancellation.cancel(useNuxtApp);
}

async function generateReport(): Promise<void> {
  loading.value = true;

  if (reportType.value.value != "select report type") {
    const queryParams = `from=${encodeURIComponent(startDate.value)}&to=${encodeURIComponent(endDate.value)}&department=${encodeURIComponent(checkStatus(selectedDepartment.value.name))}&report_type=${encodeURIComponent(reportType.value.value)}&test_status=${encodeURIComponent(checkStatus(selectedTestStatus.value.name))}&test_type=${encodeURIComponent(checkStatus(selectedtTestType.value.name))}&nlim=${encodeURIComponent(checkStatus(selectedtTestType.value.name))}`;
    console.log(selectedtTestType.value);
    const { data, error, pending }: Response = await fetchRequest({
      route: `${endpoints.dailyReports}/daily_log?${queryParams}`,
      method: "GET",
      token: `${cookie.value}`
    }, cancellation);
    loading.value = pending;
    if (data.value) {
      loading.value = false;
      data.value?.data.length == 0 &&
        useNuxtApp().$toast.warning("No data found for specified parameters");
      reportData.value = data.value;
      if (reportType.value.value == "test_record") {
        completeTests.value = data.value.completed_tests;
      }
    }
    if (error.value) {
      loading.value = false;
      console.error(error.value);
    }
  } else {
    useNuxtApp().$toast.warning(
      "Please select the report type before generating the report"
    );
    loading.value = false;
  }
}

watch(
  () => selectedDepartment.value,
  () => {
    getTestTypes();
  }
);

watch(
  () => reportType.value,
  () => {
    reportData.value = [];
  }
);

getDepartments();
getTestTypes();
getTestStatuses();
</script>

<style></style>
